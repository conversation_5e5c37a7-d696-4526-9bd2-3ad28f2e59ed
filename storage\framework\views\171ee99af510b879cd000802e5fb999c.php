<?php $__env->startSection('titlePage' , 'All Warehouses'); ?>
<?php $__env->startSection('content'); ?>
<!-- Page Header -->
<div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
    <div class="my-auto">
        <h5 class="page-title fs-21 mb-1"><?php echo e(__('categories.all categories')); ?></h5>
        <nav>
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="javascript:void(0);">dashboard</a></li>
                <li class="breadcrumb-item active" aria-current="page"><?php echo e(__('side_bar.categories')); ?></li>
            </ol>
        </nav>
    </div>

    <div class="d-flex my-xl-auto right-content align-items-center">
        <div class="pe-1 mb-xl-0">
            <button type="button" class="btn btn-info btn-icon me-2 btn-b"><i class="mdi mdi-filter-variant"></i></button>
        </div>
        <div class="pe-1 mb-xl-0">
            <button type="button" class="btn btn-danger btn-icon me-2"><i class="mdi mdi-star"></i></button>
        </div>
        <div class="pe-1 mb-xl-0">
            <button type="button" class="btn btn-warning  btn-icon me-2"><i class="mdi mdi-refresh"></i></button>
        </div>
    </div>
</div>
<!-- Page Header Close -->




<div class="row">
    <div class="col-xl">
        <div class="card custom-card">
            <div class="card-header justify-content-between">
                <div class="card-title">
                    <?php echo e(__('categories.add new category')); ?>

                </div>
            </div>
            <div class="card-body">
                <form>
                <div class="row d-flex align-items-center justify-content-center">
                        <?php echo csrf_field(); ?>
                        <div class="col-md-3">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="floatingInput" placeholder="<?php echo e(__('categories.category name')); ?>">
                                <label for="floatingInput"><?php echo e(__('categories.category name')); ?></label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="floatingPassword" placeholder="<?php echo e(__('categories.category description')); ?>">
                                <label for="floatingAddress"><?php echo e(__('categories.category description')); ?></label>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <button type="submit" class="btn btn-primary btn-block"><?php echo e(__('buttons.submit category')); ?></button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>


<div class="row">
    <div class="col-xl-12">
        <div class="card custom-card">
            <div class="card-header">
                <div class="card-title"><?php echo e(__('categories.all categories')); ?></div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="responsivemodal-DataTable" class="table table-bordered text-nowrap w-100 table-striped">
                        <thead>
                            <tr>
                                <th><?php echo e(__('categories.category name')); ?></th>
                                <th><?php echo e(__('categories.category description')); ?></th>
                                <th><?php echo e(__('categories.actions')); ?></th>
                            </tr>
                        </thead>
                        <tbody>

                            <tr>
                                <td>Michael Bruce</td>
                                <td>Javascript Developer</td>
                                <td>
                                    <!-- Action buttons like Edit, Delete, etc. -->
                                    <button class="btn btn-warning btn-sm">Edit</button>
                                    <button class="btn btn-danger btn-sm">Delete</button>
                                </td>


                            </tr>
                            <tr>
                                <td>Michael Bruce</td>
                                <td>Javascript Developer</td>
                                <td>
                                    <!-- Action buttons like Edit, Delete, etc. -->
                                    <button class="btn btn-warning btn-sm">Edit</button>
                                    <button class="btn btn-danger btn-sm">Delete</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\فيديوهات تعليميه\laravel\Stock Management System\stock_management_system\resources\views/admin/categories/create.blade.php ENDPATH**/ ?>