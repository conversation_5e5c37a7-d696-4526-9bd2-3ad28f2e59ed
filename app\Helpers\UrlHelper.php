<?php

namespace App\Helpers;

class UrlHelper
{
    /**
     * تحويل النص إلى slug مناسب للـ URL
     * يحول المسافات إلى شرطة سفلية وينظف النص
     */
    public static function createSlug($text)
    {
        // إزالة المسافات من البداية والنهاية
        $text = trim($text);
        
        // تحويل المسافات إلى شرطة سفلية
        $slug = str_replace(' ', '_', $text);
        
        // إزالة أي شرطة سفلية من النهاية
        $slug = rtrim($slug, '_');
        
        return $slug;
    }
    
    /**
     * تحويل الـ slug إلى النص الأصلي
     * يحول الشرطة السفلية إلى مسافات
     */
    public static function parseSlug($slug)
    {
        // تحويل الشرطة السفلية إلى مسافات
        $text = str_replace('_', ' ', $slug);
        
        // إزالة المسافات الزائدة من البداية والنهاية
        $text = trim($text);
        
        return $text;
    }
    
    /**
     * إنشاء رابط تعديل للمخزن
     */
    public static function warehouseEditUrl($warehouseName)
    {
        return route('admin.warehouses.edit', self::createSlug($warehouseName));
    }
}
