<div class="row">
    <div class="col-xl">
        <div class="card custom-card">
            <div class="card-header justify-content-between">
                <div class="card-title">
                    {{__('warehouses.edit warehouse')}}: {{ $name }}
                </div>
            </div>
            <div class="card-body">
                <form>
                    <div class="row d-flex align-items-center justify-content-center">
                        <div class="col-md-3">
                            <div class="form-floating mb-3">
                                <input type="text" wire:model.live="name" class="form-control" placeholder="{{__('warehouses.warehouse name')}}">
                                <label>{{__('warehouses.warehouse name')}}</label>
                            </div>
                            <div>
                                @error('name') <span class="text-danger">{{ $message }}</span> @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" wire:model.live="address" class="form-control" placeholder="{{__('warehouses.warehouse address')}}">
                                <label>{{__('warehouses.warehouse address')}}</label>
                            </div>
                            <div>
                                @error('address') <span class="text-danger">{{ $message }}</span> @enderror
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <button type="submit" wire:click.prevent="update" class="btn btn-success btn-block w-100">
                                {{__('buttons.update warehouse')}}
                            </button>
                            <a href="{{ route('admin.warehouses') }}" class="btn btn-secondary btn-block w-100 mt-2">
                                {{__('buttons.cancel')}}
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
