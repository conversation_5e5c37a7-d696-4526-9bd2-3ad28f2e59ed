<div>
    <div class="row">
    <div class="col-xl-12">
        <div class="card custom-card">
            <div class="card-header">
                <div class="card-title">{{__('warehouses.all warehoueses')}}</div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="warehouses-table" class="table table-bordered text-nowrap w-100 table-striped">
                        <thead>
                            <tr>
                                <th>{{__('warehouses.warehouse name')}}</th>
                                <th>{{__('warehouses.warehouse address')}}</th>
                                <th>{{__('warehouses.actions')}}</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($warehouses as $warehouse)
                                <tr>
                                    <td>{{$warehouse->name}}</td>
                                    <td>{{$warehouse->address}}</td>
                                    <td>
                                        <!-- Action buttons like Edit, Delete, etc. -->
                                        <a href="{{ $warehouse->edit_url }}" class="btn btn-warning btn-sm">
                                            <i class="mdi mdi-pencil"></i> {{__('buttons.edit')}}
                                        </a>
                                        <button class="btn btn-danger btn-sm" wire:click="confirmDelete({{ $warehouse->id }})" data-bs-toggle="modal" data-bs-target="#deleteModal">
                                            <i class="mdi mdi-delete"></i> {{__('buttons.delete')}}
                                        </button>
                                    </td>
                                </tr>
                            @empty
                                    <tr>
                                        <td colspan="3" class="text-center">No warehouses found</td>
                                    </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true" wire:ignore.self>
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">{{__('warehouses.delete warehouse')}}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف المخزن: <strong>{{ $warehouseToDelete?->name }}</strong>؟</p>
                <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه!</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{__('buttons.cancel')}}</button>
                <button type="button" class="btn btn-danger" wire:click="deleteWarehouse" data-bs-dismiss="modal">
                    <i class="mdi mdi-delete"></i> {{__('buttons.delete')}}
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeDataTable();
});

// إعادة تشغيل DataTable بعد تحديث Livewire
document.addEventListener('livewire:updated', function() {
    setTimeout(function() {
        initializeDataTable();
    }, 100);
});

function initializeDataTable() {
    // تدمير DataTable الموجود إذا كان موجود
    if ($.fn.DataTable.isDataTable('#warehouses-table')) {
        $('#warehouses-table').DataTable().destroy();
    }

    // إنشاء DataTable جديد
    $('#warehouses-table').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json"
        },
        "responsive": true,
        "pageLength": 10,
        "order": [[0, "asc"]],
        "searching": true,
        "paging": true,
        "info": true
    });
}
</script>
@endpush
</div>
