<div>
    <div class="row">
    <div class="col-xl-12">
        <div class="card custom-card">
            <div class="card-header">
                <div class="card-title">{{__('warehouses.all warehoueses')}}</div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="datatable-basic" class="table table-bordered text-nowrap w-100 table-striped">
                        <thead>
                            <tr>
                                <th>{{__('warehouses.warehouse name')}}</th>
                                <th>{{__('warehouses.warehouse address')}}</th>
                                <th>{{__('warehouses.actions')}}</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($warehouses as $warehouse)
                                <tr>
                                    <td>{{$warehouse->name}}</td>
                                    <td>{{$warehouse->address}}</td>
                                    <td>
                                        <!-- Action buttons like Edit, Delete, etc. -->
                                        <a href="{{ route('admin.warehouses.edit', $warehouse->name) }}" class="btn btn-warning btn-sm">
                                            <i class="mdi mdi-pencil"></i> {{__('buttons.edit')}}
                                        </a>
                                        <button class="btn btn-danger btn-sm" onclick="confirm('هل أنت متأكد من حذف هذا المخزن؟')">
                                            <i class="mdi mdi-delete"></i> {{__('buttons.delete')}}
                                        </button>
                                    </td>
                                </tr>
                            @empty
                                    <tr>
                                        <td colspan="3" class="text-center">No warehouses found</td>
                                    </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
