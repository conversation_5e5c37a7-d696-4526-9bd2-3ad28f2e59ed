<!DOCTYPE html>

<html lang="ar" dir="rtl"
    data-nav-layout="vertical" data-theme-mode="light" data-header-styles="light" data-menu-styles="light"
    data-toggled="close">

<?php echo $__env->make('admin.layouts.inc.head', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<body>

    <?php echo $__env->make('admin.layouts.inc.switcher', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>


    <!-- Loader -->
    <div id="loader" >
        <img src="<?php echo e(asset("admin/assets/images/media/loader.svg")); ?>" alt="Loading please wait a moment">
    </div>
    <!-- Loader -->

    <div class="page">
        <!-- app-header -->
        <?php echo $__env->make('admin.layouts.inc.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- /app-header -->

        <!-- Start::app-sidebar -->
        <?php echo $__env->make('admin.layouts.inc.sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- End::app-sidebar -->

        <!-- Start::app-content -->
        <div class="main-content app-content">
            <div class="container-fluid">

                <?php echo $__env->yieldContent('content'); ?>

            </div>
        </div>
        <!-- End::app-content -->

        <!-- Footer Start -->
        <?php echo $__env->make('admin.layouts.inc.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- Footer End -->

    </div>


    <!-- Scroll To Top -->
    <div class="scrollToTop">
        <span class="arrow"><i class="las la-angle-double-up"></i></span>
    </div>
    <div id="responsive-overlay"></div>
    <!-- Scroll To Top -->

    <?php echo $__env->make('admin.layouts.inc.script', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::scripts(); ?>


</body>

</html>
<?php /**PATH D:\فيديوهات تعليميه\laravel\Stock Management System\stock_management_system\resources\views/admin/layouts/master.blade.php ENDPATH**/ ?>