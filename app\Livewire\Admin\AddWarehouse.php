<?php

namespace App\Livewire\Admin;

use App\Models\Warehouse;
use Livewire\Component;
use Livewire\Attributes\Validate;


class AddWarehouse extends Component
{
    #[Validate('required|string|max:225')]
    public $name = '';
    #[Validate('required|string|max:225')]
    public $address = '';

    public function render()
    {

        return view('livewire.admin.add-warehouse');
    }

    public function store()
    {
        $this->validate();
        // Create a new warehouse instance
        Warehouse::create([
            'name' => $this->name,
            'address' => $this->address,
        ]);

        // Clear the form fields
    $this->reset('name', 'address');

    $this->dispatch('warehouseAdded');
    }
}
