<div>
    <div class="row">
    <div class="col-xl-12">
        <div class="card custom-card">
            <div class="card-header">
                <div class="card-title"><?php echo e(__('warehouses.all warehoueses')); ?></div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table id="datatable-basic" class="table table-bordered text-nowrap w-100 table-striped">
                        <thead>
                            <tr>
                                <th><?php echo e(__('warehouses.warehouse name')); ?></th>
                                <th><?php echo e(__('warehouses.warehouse address')); ?></th>
                                <th><?php echo e(__('warehouses.actions')); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $warehouses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $warehouse): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($warehouse->name); ?></td>
                                    <td><?php echo e($warehouse->address); ?></td>
                                    <td>
                                        <!-- Action buttons like Edit, Delete, etc. -->
                                        <a href="<?php echo e(route('admin.warehouses.edit', rtrim(str_replace(' ', '_', trim($warehouse->name)), '_'))); ?>" class="btn btn-warning btn-sm">
                                            <i class="mdi mdi-pencil"></i> <?php echo e(__('buttons.edit')); ?>

                                        </a>
                                        <button class="btn btn-danger btn-sm" onclick="confirm('هل أنت متأكد من حذف هذا المخزن؟')">
                                            <i class="mdi mdi-delete"></i> <?php echo e(__('buttons.delete')); ?>

                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="3" class="text-center">No warehouses found</td>
                                    </tr>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
<?php /**PATH D:\فيديوهات تعليميه\laravel\Stock Management System\stock_management_system\resources\views/livewire/admin/WarehouseList.blade.php ENDPATH**/ ?>