<?php

namespace App\Livewire\Admin;

use App\Models\Warehouse;
use Livewire\Component;
use Livewire\Attributes\Validate;

class EditWarehouse extends Component
{
    public $warehouseId;
    
    #[Validate('required|string|max:225')]
    public $name = '';
    
    #[Validate('required|string|max:225')]
    public $address = '';

    public function mount($id)
    {
        $this->warehouseId = $id;
        $warehouse = Warehouse::findOrFail($id);
        $this->name = $warehouse->name;
        $this->address = $warehouse->address;
    }

    public function render()
    {
        return view('livewire.admin.edit-warehouse');
    }

    public function update()
    {
        $this->validate();
        
        $warehouse = Warehouse::findOrFail($this->warehouseId);
        $warehouse->update([
            'name' => $this->name,
            'address' => $this->address,
        ]);

        session()->flash('success', 'تم تحديث المخزن بنجاح');
        
        return redirect()->route('admin.warehouses');
    }
}
