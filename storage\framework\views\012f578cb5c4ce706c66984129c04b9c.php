<?php $__env->startSection('titlePage' , 'Edit Warehouse'); ?>
<?php $__env->startSection('content'); ?>

<!-- Page Header -->
<div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
    <div class="my-auto">
        <h5 class="page-title fs-21 mb-1"><?php echo e(__('warehouses.edit warehouse')); ?>: <?php echo e($warehouse->name); ?></h5>
        <nav>
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="javascript:void(0);">dashboard</a></li>
                <li class="breadcrumb-item"><a href="<?php echo e(route('admin.warehouses')); ?>"><?php echo e(__('side_bar.warehouses')); ?></a></li>
                <li class="breadcrumb-item active" aria-current="page"><?php echo e(__('warehouses.edit warehouse')); ?></li>
            </ol>
        </nav>
    </div>

    <div class="d-flex my-xl-auto right-content align-items-center">
        <div class="pe-1 mb-xl-0">
            <a href="<?php echo e(route('admin.warehouses')); ?>" class="btn btn-secondary btn-icon me-2">
                <i class="mdi mdi-arrow-left"></i>
            </a>
        </div>
    </div>
</div>
<!-- Page Header Close -->


<?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('admin.edit-warehouse', ['id' => $warehouse->id]);

$__html = app('livewire')->mount($__name, $__params, 'lw-1082776473-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\فيديوهات تعليميه\laravel\Stock Management System\stock_management_system\resources\views/admin/warehouses/edit.blade.php ENDPATH**/ ?>