<div class="row">
    <div class="col-xl">
        <div class="card custom-card">
            <div class="card-header justify-content-between">
                <div class="card-title">
                    <?php echo e(__('warehouses.add new warehouse')); ?>

                </div>
            </div>
            <div class="card-body">
                <form>
                    <div class="row d-flex align-items-center justify-content-center">
                        <div class="col-md-3">
                            <div class="form-floating mb-3">
                                <input type="text" wire:model.live="name" class="form-control" placeholder="<?php echo e(__('warehouses.warehouse name')); ?>">
                                <label><?php echo e(__('warehouses.warehouse name')); ?></label>
                            </div>
                            <div>
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-danger"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" wire:model.live="address" class="form-control" placeholder="<?php echo e(__('warehouses.warehouse address')); ?>">
                                <label><?php echo e(__('warehouses.warehouse address')); ?></label>
                            </div>
                            <div>
                                <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <span class="text-danger"><?php echo e($message); ?></span> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <button type="submit" wire:click.prevent="store" class="btn btn-primary btn-block w-100">
                                <?php echo e(__('buttons.submit warehouse')); ?>

                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<?php /**PATH D:\فيديوهات تعليميه\laravel\Stock Management System\stock_management_system\resources\views/livewire/admin/add-warehouse.blade.php ENDPATH**/ ?>