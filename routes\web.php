<?php

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use <PERSON>cam<PERSON>\LaravelLocalization\Facades\LaravelLocalization;
use App\Helpers\UrlHelper;
use App\Models\Warehouse;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// routes/web.php
Auth::routes();

// Test route for Livewire (outside middleware)
Route::get('/test-livewire', function () {
    return view('admin.warehouses.create');
})->name('test.livewire');

// Admin routes
Route::prefix('dashboard')->middleware(['auth', 'admin'])->group(function () {
    // Admin Dashboard
    Route::get('/', function () {return view('admin.index');})->name('admin.dashboard');

    // Wateourses Admin Dashboard
    Route::get('/warehouses', function () {return view('admin.warehouses.create');})->name('admin.warehouses');
    Route::get('/warehouses/{name}/edit', function ($name) {
        $originalName = UrlHelper::parseSlug($name);
        $warehouse = Warehouse::where('name', $originalName)->firstOrFail();
        return view('admin.warehouses.edit', compact('warehouse'));
    })->name('admin.warehouses.edit');
    Route::get('/categories', function () {return view('admin.categories.create');})->name('admin.categories');
    Route::get('/products/create', function () {return view('admin.products.create');})->name('admin.products.create');
    Route::get('/products', function () {return view('admin.products.index');})->name('admin.products');
    Route::get('/invoice', function () {return view('admin.invoice.create');})->name('admin.invoice');
    Route::get('/invoice/sale', function () {return view('admin.invoice.sale');})->name('admin.invoice.sale');
    Route::get('/invoice/purchase', function () {return view('admin.invoice.purchase');})->name('admin.invoice.purchase');
    Route::get('/products/search', function () {return view('admin.products.search');})->name('admin.products.search');
    Route::get('/invoice/return', function () {return view('admin.invoice.return');})->name('admin.invoice.return');
    Route::get('/representatives', function () {return view('admin.representatives.index');})->name('admin.representatives.index');
    Route::get('/representatives/create', function () {return view('admin.representatives.create');})->name('admin.representatives.create');
    Route::get('/representatives/invoices', function () {return view('admin.representatives.invoices');})->name('admin.representatives.invoices');
    Route::get('/representatives/invoices/create', function () {return view('admin.representatives.invoice-create');})->name('admin.representatives.invoices.create');
    Route::get('/representatives/invoices/{id}/view', function ($id) {return view('admin.representatives.invoice-view', compact('id'));})->name('admin.representatives.invoices.view');
    Route::get('/representatives/invoices/{id}/edit', function ($id) {return view('admin.representatives.invoice-edit', compact('id'));})->name('admin.representatives.invoices.edit');

    // Temporary routes for customers and suppliers (static pages)
    Route::get('/customers', function () {return view('admin.customers.index');})->name('admin.customers.index');
    Route::get('/customers/create', function () {return view('admin.customers.create');})->name('admin.customers.create');
    Route::get('/customers/edit', function () {return view('admin.customers.edit');})->name('admin.customers.edit');
    Route::get('/customers/show', function () {return view('admin.customers.show');})->name('admin.customers.show');

    Route::get('/suppliers', function () {return view('admin.suppliers.index');})->name('admin.suppliers.index');
    Route::get('/suppliers/create', function () {return view('admin.suppliers.create');})->name('admin.suppliers.create');
    Route::get('/suppliers/edit', function () {return view('admin.suppliers.edit');})->name('admin.suppliers.edit');
    Route::get('/suppliers/show', function () {return view('admin.suppliers.show');})->name('admin.suppliers.show');
});

// Localization routes
Route::group(
    [
        'prefix' => LaravelLocalization::setLocale(),
        'middleware' => [ 'localeSessionRedirect', 'localizationRedirect', 'localeViewPath' ]
    ], function () {

        Route::get('/', function () {return view('website.index');})->name('home');

    /** ADD ALL LOCALIZED ROUTES INSIDE ADMIN ROUTS **/

});


/** OTHER PAGES THAT SHOULD NOT BE LOCALIZED **/

