<?php

namespace App\Livewire\Admin;

use App\Models\Warehouse;
use Livewire\Component;
use Livewire\Attributes\On;



class warehouseList extends Component
{
    #[On('warehouse-added')]
    public function refreshWarehouses()
    {
    }
    public function render()
    {
        $warehouses = Warehouse::all();
        return view('livewire.admin.WarehouseList', [
        'warehouses' => $warehouses,
    ]);
    }
}
