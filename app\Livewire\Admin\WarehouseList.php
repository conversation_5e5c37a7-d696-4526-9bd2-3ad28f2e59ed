<?php

namespace App\Livewire\Admin;

use App\Models\Warehouse;
use Livewire\Component;
use Livewire\Attributes\On;

class warehouseList extends Component
{
    public $warehouseToDelete = null;

    #[On('warehouseAdded')]
    public function refreshWarehouses()
    {
        $this->render();
    }

    public function confirmDelete($warehouseId)
    {
        $this->warehouseToDelete = Warehouse::find($warehouseId);
    }

    public function deleteWarehouse()
    {
        if ($this->warehouseToDelete) {
            $this->warehouseToDelete->delete();
            $this->warehouseToDelete = null;

            session()->flash('success', 'تم حذف المخزن بنجاح');

            // إعادة تحميل القائمة
            $this->render();
        }
    }

    public function render()
    {
        $warehouses = Warehouse::all();
        return view('livewire.admin.WarehouseList', [
            'warehouses' => $warehouses,
        ]);
    }
}
